#ifndef _aom_av1_h_
#define _aom_av1_h_

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// AV1 Profile constants
#define AV1_PROFILE_MAIN        0
#define AV1_PROFILE_HIGH        1
#define AV1_PROFILE_PROFESSIONAL 2

// AV1 Level constants
#define AV1_LEVEL_2_0   0
#define AV1_LEVEL_2_1   1
#define AV1_LEVEL_2_2   2
#define AV1_LEVEL_2_3   3
#define AV1_LEVEL_3_0   4
#define AV1_LEVEL_3_1   5
#define AV1_LEVEL_3_2   6
#define AV1_LEVEL_3_3   7
#define AV1_LEVEL_4_0   8
#define AV1_LEVEL_4_1   9
#define AV1_LEVEL_4_2   10
#define AV1_LEVEL_4_3   11
#define AV1_LEVEL_5_0   12
#define AV1_LEVEL_5_1   13
#define AV1_LEVEL_5_2   14
#define AV1_LEVEL_5_3   15
#define AV1_LEVEL_6_0   16
#define AV1_LEVEL_6_1   17
#define AV1_LEVEL_6_2   18
#define AV1_LEVEL_6_3   19
#define AV1_LEVEL_7_0   20
#define AV1_LEVEL_7_1   21
#define AV1_LEVEL_7_2   22
#define AV1_LEVEL_7_3   23

// AV1 OBU (Open Bitstream Unit) types
enum av1_obu_type_t
{
    AV1_OBU_SEQUENCE_HEADER = 1,
    AV1_OBU_TEMPORAL_DELIMITER = 2,
    AV1_OBU_FRAME_HEADER = 3,
    AV1_OBU_TILE_GROUP = 4,
    AV1_OBU_METADATA = 5,
    AV1_OBU_FRAME = 6,
    AV1_OBU_REDUNDANT_FRAME_HEADER = 7,
    AV1_OBU_TILE_LIST = 8,
    AV1_OBU_PADDING = 15,
};

// AV1 Color primaries
enum av1_color_primaries_t
{
    AV1_CP_BT_709 = 1,
    AV1_CP_UNSPECIFIED = 2,
    AV1_CP_BT_470_M = 4,
    AV1_CP_BT_470_B_G = 5,
    AV1_CP_BT_601 = 6,
    AV1_CP_SMPTE_240 = 7,
    AV1_CP_GENERIC_FILM = 8,
    AV1_CP_BT_2020 = 9,
    AV1_CP_XYZ = 10,
    AV1_CP_SMPTE_431 = 11,
    AV1_CP_SMPTE_432 = 12,
    AV1_CP_EBU_3213 = 22,
};

// AV1 Sequence header structure
struct av1_sequence_header_t
{
    uint8_t seq_profile;
    uint8_t still_picture;
    uint8_t reduced_still_picture_header;
    uint8_t timing_info_present_flag;
    uint8_t decoder_model_info_present_flag;
    uint8_t initial_display_delay_present_flag;
    uint8_t operating_points_cnt_minus_1;
    
    uint32_t max_frame_width_minus_1;
    uint32_t max_frame_height_minus_1;
    uint8_t frame_id_numbers_present_flag;
    uint8_t use_128x128_superblock;
    uint8_t enable_filter_intra;
    uint8_t enable_intra_edge_filter;
    uint8_t enable_interintra_compound;
    uint8_t enable_masked_compound;
    uint8_t enable_warped_motion;
    uint8_t enable_dual_filter;
    uint8_t enable_order_hint;
    uint8_t enable_jnt_comp;
    uint8_t enable_ref_frame_mvs;
    uint8_t seq_choose_screen_content_tools;
    uint8_t seq_force_screen_content_tools;
    uint8_t seq_choose_integer_mv;
    uint8_t seq_force_integer_mv;
    uint8_t order_hint_bits_minus_1;
    uint8_t enable_superres;
    uint8_t enable_cdef;
    uint8_t enable_restoration;
    
    // Color config
    uint8_t high_bitdepth;
    uint8_t twelve_bit;
    uint8_t mono_chrome;
    uint8_t color_description_present_flag;
    uint8_t color_primaries;
    uint8_t transfer_characteristics;
    uint8_t matrix_coefficients;
    uint8_t color_range;
    uint8_t subsampling_x;
    uint8_t subsampling_y;
    uint8_t chroma_sample_position;
    uint8_t separate_uv_delta_q;
};

// AV1 decoder configuration record
struct aom_av1_t
{
    uint8_t version;
    uint8_t seq_profile;
    uint8_t seq_level_idx_0;
    uint8_t seq_tier_0;
    uint8_t high_bitdepth;
    uint8_t twelve_bit;
    uint8_t monochrome;
    uint8_t chroma_subsampling_x;
    uint8_t chroma_subsampling_y;
    uint8_t chroma_sample_position;
    uint8_t initial_presentation_delay_present;
    uint8_t initial_presentation_delay_minus_one;
    
    // Configuration OBUs
    uint8_t *config_obus;
    size_t config_obus_length;
};

// AV1 parsing functions
int aom_av1_codec_configuration_record_load(const uint8_t* data, size_t bytes, struct aom_av1_t* av1);
int aom_av1_codec_configuration_record_save(const struct aom_av1_t* av1, uint8_t* data, size_t bytes);

// AV1 sequence header parsing
int aom_av1_sequence_header_parse(const uint8_t* data, size_t bytes, struct av1_sequence_header_t* seq);

// OBU parsing
int aom_av1_obu_parse(const uint8_t* data, size_t bytes, enum av1_obu_type_t* type, size_t* obu_size);

// Utility functions
int aom_av1_profile_level(const struct aom_av1_t* av1);
enum av1_obu_type_t aom_av1_obu_type(const uint8_t* obu);

// Memory management
struct aom_av1_t* aom_av1_create(void);
void aom_av1_destroy(struct aom_av1_t* av1);

#ifdef __cplusplus
}
#endif

#endif /* !_aom_av1_h_ */
