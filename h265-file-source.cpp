#include "h265-file-source.h"
#include "sys/base64.h"
#include "rtp-profile.h"
#include "rtp-payload.h"
#include <assert.h>
#include <stdio.h>

extern "C" uint32_t rtp_ssrc(void);

H265FileSource::H265FileSource(const char *file)
    : m_file(file)
{
    m_speed = 1.0;
    m_status = 0;
    m_rtp_clock = 0;
    m_rtcp_clock = 0;
    m_timestamp = 0;
    m_pos = 0;

    uint32_t ssrc = rtp_ssrc();
    static struct rtp_payload_t s_rtpfunc = {
        H265FileSource::RTPAlloc,
        H265FileSource::RTPFree,
        H265FileSource::RTPPacket,
    };
    m_rtppacker = rtp_payload_encode_create(RTP_PAYLOAD_H265, "H265", (uint16_t)ssrc, ssrc, &s_rtpfunc, this);

    struct rtp_event_t event;
    event.on_rtcp = OnRTCPEvent;
    m_rtp = rtp_create(&event, this, ssrc, m_timestamp, 90000, 4*1024, 1);
    rtp_set_info(m_rtp, "RTSPServer", "h265.file");
}

H265FileSource::~H265FileSource()
{
    if (m_rtppacker) {
        rtp_payload_encode_destroy(m_rtppacker);
        m_rtppacker = NULL;
    }
    
    if (m_rtp) {
        rtp_destroy(m_rtp);
        m_rtp = NULL;
    }
}

int H265FileSource::Play()
{
    m_status = 1;
    m_rtp_clock = time64_now();
    return 0;
}

int H265FileSource::Pause()
{
    m_status = 2;
    return 0;
}

int H265FileSource::Seek(int64_t pos)
{
    m_pos = pos;
    return 0;
}

int H265FileSource::SetSpeed(double speed)
{
    m_speed = speed;
    return 0;
}

int H265FileSource::GetDuration(int64_t& duration) const
{
    // TODO: Parse H265 file to get actual duration
    // For now, return a default duration
    duration = 60000; // 60 seconds in milliseconds
    return 0;
}

int H265FileSource::GetSDPMedia(std::string& sdp) const
{
    // Basic H265 SDP
    static const char* pattern = 
        "m=video 0 RTP/AVP 96\r\n"
        "a=rtpmap:96 H265/90000\r\n"
        "a=fmtp:96 profile-id=1\r\n"
        "a=control:track1\r\n";
    
    sdp = pattern;
    return 0;
}

int H265FileSource::GetRTPInfo(const char* uri, char *rtpinfo, size_t bytes) const
{
    uint16_t seq;
    uint32_t timestamp;
    
    rtp_payload_encode_getinfo(m_rtppacker, &seq, &timestamp);
    
    // url=rtsp://video.example.com/twister/video;seq=12312232;rtptime=78712811
    snprintf(rtpinfo, bytes, "url=%s;seq=%hu;rtptime=%u", uri, seq, timestamp);
    return 0;
}

int H265FileSource::SetTransport(const char* track, std::shared_ptr<IRTPTransport> transport)
{
    m_transport = transport;
    return 0;
}

void H265FileSource::OnRTCPEvent(void* param, const struct rtcp_msg_t* msg)
{
    H265FileSource* self = (H265FileSource*)param;
    self->OnRTCPEvent(msg);
}

void H265FileSource::OnRTCPEvent(const struct rtcp_msg_t* msg)
{
    // Handle RTCP events
}

int H265FileSource::SendRTCP()
{
    // TODO: Send RTCP packets
    time64_t clock = time64_now();
    int interval = rtp_rtcp_interval(m_rtp);
    if (0 == m_rtcp_clock || m_rtcp_clock + interval < clock) {
        char rtcp[1024] = {0};
        size_t n = rtp_rtcp_report(m_rtp, rtcp, sizeof(rtcp));
        
        // Send RTCP packet
        if (m_transport && n > 0) {
            m_transport->Send(true, rtcp, n);
        }
        
        m_rtcp_clock = clock;
    }
    return 0;
}

void* H265FileSource::RTPAlloc(void* param, int bytes)
{
    H265FileSource* self = (H265FileSource*)param;
    assert(bytes <= sizeof(self->m_packet));
    return self->m_packet;
}

void H265FileSource::RTPFree(void* param, void *packet)
{
    // Static buffer, no need to free
}

int H265FileSource::RTPPacket(void* param, const void *packet, int bytes, uint32_t timestamp, int flags)
{
    H265FileSource* self = (H265FileSource*)param;
    
    if (self->m_transport) {
        // Send RTP packet
        int ret = self->m_transport->Send(false, packet, bytes);
        
        // Update RTP statistics
        rtp_onsend(self->m_rtp, packet, bytes);
        
        // Send RTCP if needed
        self->SendRTCP();
        
        return ret;
    }
    
    return 0;
}
