#ifndef _sdp_options_h_
#define _sdp_options_h_

#ifdef __cplusplus
extern "C" {
#endif

// SDP options structure
struct sdp_options_t
{
    int rtp_over_tcp;       // RTP over TCP (interleaved)
    int multicast;          // Multicast support
    int unicast;            // Unicast support
    int aggregate_control;  // Aggregate control support
    int play_notify;        // Play notification support
    int record_notify;      // Record notification support
    int pause_notify;       // Pause notification support
    int setup_notify;       // Setup notification support
    int teardown_notify;    // Teardown notification support
    int get_parameter;      // GET_PARAMETER support
    int set_parameter;      // SET_PARAMETER support
    int options;            // OPTIONS support
    int describe;           // DESCRIBE support
    int announce;           // ANNOUNCE support
    int record;             // RECORD support
    int redirect;           // REDIRECT support
};

// Default SDP options
extern const struct sdp_options_t sdp_options_default;

// Initialize SDP options with default values
void sdp_options_init(struct sdp_options_t* options);

// Parse SDP options from string
int sdp_options_parse(struct sdp_options_t* options, const char* str);

// Convert SDP options to string
int sdp_options_stringify(const struct sdp_options_t* options, char* str, size_t len);

#ifdef __cplusplus
}
#endif

#endif /* !_sdp_options_h_ */
