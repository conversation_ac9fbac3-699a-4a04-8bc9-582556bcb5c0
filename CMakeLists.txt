cmake_minimum_required(VERSION 3.10)
project(rtsp-server-test VERSION 1.0.0)

# Set C++ standard
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set C standard
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Build options
option(ENABLE_FFMPEG "Enable FFmpeg support" OFF)
option(BUILD_SHARED_LIBS "Build shared libraries" OFF)
option(ENABLE_DEBUG "Enable debug output" OFF)

# Compiler flags
if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra")
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O0")
        set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -g -O0")
    else()
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O2")
        set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -O2")
    endif()
endif()

# Platform specific definitions and libraries
if(WIN32)
    add_definitions(-DOS_WINDOWS -D_WIN32_WINNT=0x0600)
    set(PLATFORM_LIBS ws2_32 wsock32)
elseif(APPLE)
    add_definitions(-DOS_MAC)
    set(PLATFORM_LIBS)
else()
    add_definitions(-DOS_LINUX)
    set(PLATFORM_LIBS pthread)
endif()

# Debug/Release definitions
if(CMAKE_BUILD_TYPE STREQUAL "Debug" OR ENABLE_DEBUG)
    add_definitions(-D_DEBUG -DDEBUG)
endif()

# FFmpeg support
if(ENABLE_FFMPEG)
    add_definitions(-D_HAVE_FFMPEG_)
endif()

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/librtp/include
    ${CMAKE_CURRENT_SOURCE_DIR}/librtsp/include
)

# External dependencies configuration
# You can set these via command line: cmake -DEXTERNAL_ROOT=/path/to/deps ..
set(EXTERNAL_ROOT "" CACHE PATH "Root directory for external dependencies")

if(EXTERNAL_ROOT)
    include_directories(${EXTERNAL_ROOT}/include)
    link_directories(${EXTERNAL_ROOT}/lib)
    message(STATUS "Using external dependencies from: ${EXTERNAL_ROOT}")
endif()

# Try to find some common system libraries
find_library(PTHREAD_LIB pthread)
if(NOT WIN32 AND PTHREAD_LIB)
    list(APPEND PLATFORM_LIBS ${PTHREAD_LIB})
endif()

# External libraries that may need to be linked
# These are commented out by default - uncomment and adjust as needed
set(EXTERNAL_LIBS
    # Uncomment the libraries you have available:
    # cstringext
    # sockutil
    # aio-worker
    # ntp-time
    # uri-parse
    # urlcodec
    # path
    # base64
    # time64
    # sockpair
    # ip-route
    # http-parser
    # rfc822-datetime
)

# RTP Library source files
file(GLOB RTP_SOURCES
    librtp/source/*.c
    librtp/payload/*.c
)

# RTSP Library source files
file(GLOB RTSP_SOURCES
    librtsp/source/*.c
    librtsp/source/client/*.c
    librtsp/source/server/*.c
    librtsp/source/server/aio/*.c
    librtsp/source/sdp/*.c
    librtsp/source/utils/*.c
)

# Project source files
set(PROJECT_SOURCES
    rtsp-server-test.cpp
    h264-file-source.cpp
    h264-file-reader.cpp
    h265-file-source.cpp
    h265-file-reader.cpp
    rtp-udp-transport.cpp
    rtp-tcp-transport.cpp
    rtsp-utils.cpp
    http-reason.c
    sys/ntp-time.c
)

# Create RTP static library
add_library(rtp STATIC ${RTP_SOURCES})
target_include_directories(rtp PUBLIC librtp/include)

# Create RTSP static library
add_library(rtsp STATIC ${RTSP_SOURCES})
target_include_directories(rtsp PUBLIC librtsp/include)
target_link_libraries(rtsp rtp)

# Main executable
add_executable(rtsp-server-test ${PROJECT_SOURCES})

# Link libraries
target_link_libraries(rtsp-server-test
    rtsp
    rtp
    ${EXTERNAL_LIBS}
    ${PLATFORM_LIBS}
)

# FFmpeg support (optional)
if(ENABLE_FFMPEG)
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(FFMPEG
            libavformat
            libavcodec
            libavutil
            libswscale
        )
        if(FFMPEG_FOUND)
            target_include_directories(rtsp-server-test PRIVATE ${FFMPEG_INCLUDE_DIRS})
            target_link_libraries(rtsp-server-test ${FFMPEG_LIBRARIES})
            message(STATUS "FFmpeg libraries found and linked")
        else()
            message(WARNING "FFmpeg requested but not found via pkg-config")
        endif()
    else()
        message(WARNING "FFmpeg requested but pkg-config not available")
    endif()
endif()

# Test script creation removed - create manually if needed

# Installation
install(TARGETS rtsp-server-test DESTINATION bin)
install(TARGETS rtp rtsp DESTINATION lib)
install(DIRECTORY librtp/include/ DESTINATION include/librtp FILES_MATCHING PATTERN "*.h")
install(DIRECTORY librtsp/include/ DESTINATION include/librtsp FILES_MATCHING PATTERN "*.h")

# Print configuration summary
message(STATUS "=== RTSP Server Build Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "C compiler: ${CMAKE_C_COMPILER}")
message(STATUS "Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "Shared libraries: ${BUILD_SHARED_LIBS}")
if(ENABLE_FFMPEG)
    message(STATUS "FFmpeg support: ENABLED")
else()
    message(STATUS "FFmpeg support: DISABLED")
endif()
if(EXTERNAL_ROOT)
    message(STATUS "External deps root: ${EXTERNAL_ROOT}")
else()
    message(STATUS "External deps root: NOT SET")
endif()
message(STATUS "=======================================")

# Warning about external dependencies
if(NOT EXTERNAL_ROOT)
    message(WARNING "
    External dependencies not configured!

    This project requires external libraries that are not included.
    Please either:
    1. Set EXTERNAL_ROOT: cmake -DEXTERNAL_ROOT=/path/to/deps ..
    2. Install system packages for the required libraries
    3. Modify EXTERNAL_LIBS in CMakeLists.txt to link specific libraries

    See BUILD_INSTRUCTIONS.md for detailed information.
    ")
endif()
