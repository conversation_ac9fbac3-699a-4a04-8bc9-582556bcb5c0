
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/rtspSRV/librtp/payload/rtp-av1-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-av1-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h264-bitstream.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h264-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h264-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h265-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h265-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h266-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h266-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-mp4a-latm-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-mp4a-latm-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-mp4v-es-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-mp4v-es-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-mpeg1or2es-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-mpeg1or2es-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-mpeg4-generic-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-mpeg4-generic-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-payload-helper.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-payload.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-ps-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-ts-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-ts-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-vp8-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-vp8-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-vp9-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-vp9-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-abs-send-time.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-abs-send-time.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-abs-send-time.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-absolute-capture-time.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-absolute-capture-time.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-absolute-capture-time.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-color-space.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-color-space.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-color-space.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-csrc-audio-level.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-csrc-audio-level.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-csrc-audio-level.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-frame-marking.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-frame-marking.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-frame-marking.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-inband-cn.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-inband-cn.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-inband-cn.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-playout-delay.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-playout-delay.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-playout-delay.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-sdes.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-sdes.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-sdes.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-ssrc-audio-level.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-ssrc-audio-level.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-ssrc-audio-level.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-toffset.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-toffset.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-toffset.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-transport-wide-cc.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-transport-wide-cc.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-transport-wide-cc.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-video-content-type.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-video-content-type.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-video-content-type.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-video-frame-tracking-id.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-video-frame-tracking-id.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-video-frame-tracking-id.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-video-layers-allocation.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-video-layers-allocation.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-video-layers-allocation.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-video-orientation.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-video-orientation.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-video-orientation.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext-video-timing.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-video-timing.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext-video-timing.c.o.d"
  "/home/<USER>/rtspSRV/librtp/rtpext/rtp-ext.c" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/rtpext/rtp-ext.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-app.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-bye.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-interval.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-psfb.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-rr.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-rtpfb.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-sdec.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-sr.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-xr.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp.c" "CMakeFiles/rtp.dir/librtp/source/rtcp.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-demuxer.c" "CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-member-list.c" "CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-member.c" "CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-packet.c" "CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-profile.c" "CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-queue.c" "CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-ssrc.c" "CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-time.c" "CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp.c" "CMakeFiles/rtp.dir/librtp/source/rtp.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp.c.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
