# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/rtspSRV

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/rtspSRV/build

# Include any dependencies generated for this target.
include CMakeFiles/rtsp.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/rtsp.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/rtsp.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/rtsp.dir/flags.make

CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.o: ../librtsp/source/client/rtp-over-rtsp.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtp-over-rtsp.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtp-over-rtsp.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtp-over-rtsp.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.o: ../librtsp/source/client/rtsp-client-announce.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-announce.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-announce.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-announce.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.o: ../librtsp/source/client/rtsp-client-auth.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-auth.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-auth.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-auth.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.o: ../librtsp/source/client/rtsp-client-describe.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-describe.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-describe.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-describe.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.o: ../librtsp/source/client/rtsp-client-get-parameter.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-get-parameter.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-get-parameter.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-get-parameter.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.o: ../librtsp/source/client/rtsp-client-options.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-options.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-options.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-options.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.o: ../librtsp/source/client/rtsp-client-pause.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-pause.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-pause.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-pause.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.o: ../librtsp/source/client/rtsp-client-play.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-play.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-play.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-play.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.o: ../librtsp/source/client/rtsp-client-record.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-record.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-record.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-record.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.o: ../librtsp/source/client/rtsp-client-set-parameter.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-set-parameter.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-set-parameter.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-set-parameter.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.o: ../librtsp/source/client/rtsp-client-setup.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-setup.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-setup.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-setup.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.o: ../librtsp/source/client/rtsp-client-teardown.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-teardown.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-teardown.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client-teardown.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.s

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.o: ../librtsp/source/client/rtsp-client.c
CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.o -c /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client.c

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client.c > CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.i

CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/client/rtsp-client.c -o CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.s

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.o: ../librtsp/source/rtsp-header-range.c
CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.o -c /home/<USER>/rtspSRV/librtsp/source/rtsp-header-range.c

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/rtsp-header-range.c > CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.i

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/rtsp-header-range.c -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.s

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.o: ../librtsp/source/rtsp-header-rtp-info.c
CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.o -c /home/<USER>/rtspSRV/librtsp/source/rtsp-header-rtp-info.c

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/rtsp-header-rtp-info.c > CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.i

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/rtsp-header-rtp-info.c -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.s

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.o: ../librtsp/source/rtsp-header-session.c
CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.o -c /home/<USER>/rtspSRV/librtsp/source/rtsp-header-session.c

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/rtsp-header-session.c > CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.i

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/rtsp-header-session.c -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.s

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.o: ../librtsp/source/rtsp-header-transport.c
CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.o -c /home/<USER>/rtspSRV/librtsp/source/rtsp-header-transport.c

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/rtsp-header-transport.c > CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.i

CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/rtsp-header-transport.c -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.s

CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.o: ../librtsp/source/rtsp-media.c
CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.o -c /home/<USER>/rtspSRV/librtsp/source/rtsp-media.c

CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/rtsp-media.c > CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.i

CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/rtsp-media.c -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.s

CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.o: ../librtsp/source/rtsp-multicast.c
CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.o -c /home/<USER>/rtspSRV/librtsp/source/rtsp-multicast.c

CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/rtsp-multicast.c > CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.i

CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/rtsp-multicast.c -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.s

CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.o: ../librtsp/source/rtsp-reason.c
CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.o -c /home/<USER>/rtspSRV/librtsp/source/rtsp-reason.c

CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/rtsp-reason.c > CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.i

CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/rtsp-reason.c -o CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.o: ../librtsp/source/server/aio/rtsp-server-listen.c
CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/aio/rtsp-server-listen.c

CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/aio/rtsp-server-listen.c > CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/aio/rtsp-server-listen.c -o CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.o: ../librtsp/source/server/aio/rtsp-server-tcp.c
CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/aio/rtsp-server-tcp.c

CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/aio/rtsp-server-tcp.c > CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/aio/rtsp-server-tcp.c -o CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.o: ../librtsp/source/server/aio/rtsp-server-udp.c
CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/aio/rtsp-server-udp.c

CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/aio/rtsp-server-udp.c > CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/aio/rtsp-server-udp.c -o CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.o: ../librtsp/source/server/rtsp-server-announce.c
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-announce.c

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-announce.c > CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-announce.c -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.o: ../librtsp/source/server/rtsp-server-describe.c
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-describe.c

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-describe.c > CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-describe.c -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.o: ../librtsp/source/server/rtsp-server-handler.c
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-handler.c

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-handler.c > CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-handler.c -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.o: ../librtsp/source/server/rtsp-server-options.c
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-options.c

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-options.c > CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-options.c -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.o: ../librtsp/source/server/rtsp-server-parameter.c
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-parameter.c

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-parameter.c > CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-parameter.c -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.o: ../librtsp/source/server/rtsp-server-pause.c
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-pause.c

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-pause.c > CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-pause.c -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.o: ../librtsp/source/server/rtsp-server-play.c
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-play.c

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-play.c > CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-play.c -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.o: ../librtsp/source/server/rtsp-server-record.c
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-record.c

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-record.c > CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-record.c -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.o: ../librtsp/source/server/rtsp-server-setup.c
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-setup.c

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-setup.c > CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-setup.c -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.o: ../librtsp/source/server/rtsp-server-teardown.c
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-teardown.c

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-teardown.c > CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server-teardown.c -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.s

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.o: ../librtsp/source/server/rtsp-server.c
CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.o -c /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server.c

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server.c > CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.i

CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/server/rtsp-server.c -o CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.s

CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.o: ../librtsp/source/utils/rtp-sender.c
CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.o -c /home/<USER>/rtspSRV/librtsp/source/utils/rtp-sender.c

CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/utils/rtp-sender.c > CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.i

CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/utils/rtp-sender.c -o CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.s

CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.o: ../librtsp/source/utils/rtsp-demuxer.c
CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.o -c /home/<USER>/rtspSRV/librtsp/source/utils/rtsp-demuxer.c

CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/utils/rtsp-demuxer.c > CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.i

CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/utils/rtsp-demuxer.c -o CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.s

CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.o: CMakeFiles/rtsp.dir/flags.make
CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.o: ../librtsp/source/utils/rtsp-muxer.c
CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.o: CMakeFiles/rtsp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building C object CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.o -MF CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.o.d -o CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.o -c /home/<USER>/rtspSRV/librtsp/source/utils/rtsp-muxer.c

CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtsp/source/utils/rtsp-muxer.c > CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.i

CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtsp/source/utils/rtsp-muxer.c -o CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.s

# Object files for target rtsp
rtsp_OBJECTS = \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.o" \
"CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.o"

# External object files for target rtsp
rtsp_EXTERNAL_OBJECTS =

librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtp-over-rtsp.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-announce.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-auth.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-describe.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-get-parameter.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-options.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-pause.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-play.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-record.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-set-parameter.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-setup.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client-teardown.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/client/rtsp-client.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-listen.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-tcp.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/aio/rtsp-server-udp.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.o
librtsp.a: CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.o
librtsp.a: CMakeFiles/rtsp.dir/build.make
librtsp.a: CMakeFiles/rtsp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Linking C static library librtsp.a"
	$(CMAKE_COMMAND) -P CMakeFiles/rtsp.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/rtsp.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/rtsp.dir/build: librtsp.a
.PHONY : CMakeFiles/rtsp.dir/build

CMakeFiles/rtsp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/rtsp.dir/cmake_clean.cmake
.PHONY : CMakeFiles/rtsp.dir/clean

CMakeFiles/rtsp.dir/depend:
	cd /home/<USER>/rtspSRV/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/rtspSRV /home/<USER>/rtspSRV /home/<USER>/rtspSRV/build /home/<USER>/rtspSRV/build /home/<USER>/rtspSRV/build/CMakeFiles/rtsp.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/rtsp.dir/depend

