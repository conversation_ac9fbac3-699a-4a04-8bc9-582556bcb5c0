# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/rtspSRV

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/rtspSRV/build

# Include any dependencies generated for this target.
include CMakeFiles/rtsp-server-test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/rtsp-server-test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/rtsp-server-test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/rtsp-server-test.dir/flags.make

CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.o: CMakeFiles/rtsp-server-test.dir/flags.make
CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.o: ../rtsp-server-test.cpp
CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.o: CMakeFiles/rtsp-server-test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.o -MF CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.o.d -o CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.o -c /home/<USER>/rtspSRV/rtsp-server-test.cpp

CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/rtspSRV/rtsp-server-test.cpp > CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.i

CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/rtspSRV/rtsp-server-test.cpp -o CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.s

CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.o: CMakeFiles/rtsp-server-test.dir/flags.make
CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.o: ../h264-file-source.cpp
CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.o: CMakeFiles/rtsp-server-test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.o -MF CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.o.d -o CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.o -c /home/<USER>/rtspSRV/h264-file-source.cpp

CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/rtspSRV/h264-file-source.cpp > CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.i

CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/rtspSRV/h264-file-source.cpp -o CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.s

CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.o: CMakeFiles/rtsp-server-test.dir/flags.make
CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.o: ../h264-file-reader.cpp
CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.o: CMakeFiles/rtsp-server-test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.o -MF CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.o.d -o CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.o -c /home/<USER>/rtspSRV/h264-file-reader.cpp

CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/rtspSRV/h264-file-reader.cpp > CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.i

CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/rtspSRV/h264-file-reader.cpp -o CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.s

CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.o: CMakeFiles/rtsp-server-test.dir/flags.make
CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.o: ../h265-file-source.cpp
CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.o: CMakeFiles/rtsp-server-test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.o -MF CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.o.d -o CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.o -c /home/<USER>/rtspSRV/h265-file-source.cpp

CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/rtspSRV/h265-file-source.cpp > CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.i

CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/rtspSRV/h265-file-source.cpp -o CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.s

CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.o: CMakeFiles/rtsp-server-test.dir/flags.make
CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.o: ../h265-file-reader.cpp
CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.o: CMakeFiles/rtsp-server-test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.o -MF CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.o.d -o CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.o -c /home/<USER>/rtspSRV/h265-file-reader.cpp

CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/rtspSRV/h265-file-reader.cpp > CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.i

CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/rtspSRV/h265-file-reader.cpp -o CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.s

CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.o: CMakeFiles/rtsp-server-test.dir/flags.make
CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.o: ../rtp-udp-transport.cpp
CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.o: CMakeFiles/rtsp-server-test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.o -MF CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.o.d -o CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.o -c /home/<USER>/rtspSRV/rtp-udp-transport.cpp

CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/rtspSRV/rtp-udp-transport.cpp > CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.i

CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/rtspSRV/rtp-udp-transport.cpp -o CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.s

CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.o: CMakeFiles/rtsp-server-test.dir/flags.make
CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.o: ../rtp-tcp-transport.cpp
CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.o: CMakeFiles/rtsp-server-test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.o -MF CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.o.d -o CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.o -c /home/<USER>/rtspSRV/rtp-tcp-transport.cpp

CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/rtspSRV/rtp-tcp-transport.cpp > CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.i

CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/rtspSRV/rtp-tcp-transport.cpp -o CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.s

CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.o: CMakeFiles/rtsp-server-test.dir/flags.make
CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.o: ../rtsp-utils.cpp
CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.o: CMakeFiles/rtsp-server-test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.o -MF CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.o.d -o CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.o -c /home/<USER>/rtspSRV/rtsp-utils.cpp

CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/rtspSRV/rtsp-utils.cpp > CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.i

CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/rtspSRV/rtsp-utils.cpp -o CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.s

CMakeFiles/rtsp-server-test.dir/http-reason.c.o: CMakeFiles/rtsp-server-test.dir/flags.make
CMakeFiles/rtsp-server-test.dir/http-reason.c.o: ../http-reason.c
CMakeFiles/rtsp-server-test.dir/http-reason.c.o: CMakeFiles/rtsp-server-test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/rtsp-server-test.dir/http-reason.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtsp-server-test.dir/http-reason.c.o -MF CMakeFiles/rtsp-server-test.dir/http-reason.c.o.d -o CMakeFiles/rtsp-server-test.dir/http-reason.c.o -c /home/<USER>/rtspSRV/http-reason.c

CMakeFiles/rtsp-server-test.dir/http-reason.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtsp-server-test.dir/http-reason.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/http-reason.c > CMakeFiles/rtsp-server-test.dir/http-reason.c.i

CMakeFiles/rtsp-server-test.dir/http-reason.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtsp-server-test.dir/http-reason.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/http-reason.c -o CMakeFiles/rtsp-server-test.dir/http-reason.c.s

# Object files for target rtsp-server-test
rtsp__server__test_OBJECTS = \
"CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.o" \
"CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.o" \
"CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.o" \
"CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.o" \
"CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.o" \
"CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.o" \
"CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.o" \
"CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.o" \
"CMakeFiles/rtsp-server-test.dir/http-reason.c.o"

# External object files for target rtsp-server-test
rtsp__server__test_EXTERNAL_OBJECTS =

rtsp-server-test: CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.o
rtsp-server-test: CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.o
rtsp-server-test: CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.o
rtsp-server-test: CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.o
rtsp-server-test: CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.o
rtsp-server-test: CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.o
rtsp-server-test: CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.o
rtsp-server-test: CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.o
rtsp-server-test: CMakeFiles/rtsp-server-test.dir/http-reason.c.o
rtsp-server-test: CMakeFiles/rtsp-server-test.dir/build.make
rtsp-server-test: librtsp.a
rtsp-server-test: librtp.a
rtsp-server-test: /usr/lib/x86_64-linux-gnu/libpthread.a
rtsp-server-test: CMakeFiles/rtsp-server-test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Linking CXX executable rtsp-server-test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/rtsp-server-test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/rtsp-server-test.dir/build: rtsp-server-test
.PHONY : CMakeFiles/rtsp-server-test.dir/build

CMakeFiles/rtsp-server-test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/rtsp-server-test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/rtsp-server-test.dir/clean

CMakeFiles/rtsp-server-test.dir/depend:
	cd /home/<USER>/rtspSRV/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/rtspSRV /home/<USER>/rtspSRV /home/<USER>/rtspSRV/build /home/<USER>/rtspSRV/build /home/<USER>/rtspSRV/build/CMakeFiles/rtsp-server-test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/rtsp-server-test.dir/depend

